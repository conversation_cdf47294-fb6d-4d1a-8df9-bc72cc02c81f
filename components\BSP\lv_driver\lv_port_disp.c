/**
 * @file lv_port_disp_templ.c
 *
 */

/*Copy this file as "lv_port_disp.c" and set this value to "1" to enable content*/
#if 1

/*********************
 *      INCLUDES
 *********************/
#include "lv_port_disp.h"
#include "../RGBLCD/ltdc.h"
#include <stdbool.h>
#include <string.h>
#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"

/*********************
 *      DEFINES
 *********************/
#ifndef MY_DISP_HOR_RES
    #warning Please define or replace the macro MY_DISP_HOR_RES with the actual screen width, default value 320 is used for now.
    #define MY_DISP_HOR_RES    800
#endif

#ifndef MY_DISP_VER_RES
    #warning Please define or replace the macro MY_DISP_HOR_RES with the actual screen height, default value 240 is used for now.
    #define MY_DISP_VER_RES    480
#endif

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 *  STATIC PROTOTYPES
 **********************/
static void disp_init(void);
static void monitor_cb(lv_disp_drv_t * disp_drv, uint32_t time, uint32_t px);
static void disp_flush(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p);
//static void gpu_fill(lv_disp_drv_t * disp_drv, lv_color_t * dest_buf, lv_coord_t dest_width,
//        const lv_area_t * fill_area, lv_color_t color);

/**********************
 *  STATIC VARIABLES
 **********************/

/**********************
 *      MACROS
 **********************/

/**********************
 *   GLOBAL FUNCTIONS
 **********************/

void lv_port_disp_init(void)
{
    /*-------------------------
     * Initialize your display
     * -----------------------*/
    disp_init();

    /*-----------------------------
     * Create a buffer for drawing
     *----------------------------*/

    /**
     * LVGL requires a buffer where it internally draws the widgets.
     * Later this buffer will passed to your display driver's `flush_cb` to copy its content to your display.
     * The buffer has to be greater than 1 display row
     *
     * There are 3 buffering configurations:
     * 1. Create ONE buffer:
     *      LVGL will draw the display's content here and writes it to your display
     *
     * 2. Create TWO buffer:
     *      LVGL will draw the display's content to a buffer and writes it your display.
     *      You should use DMA to write the buffer's content to the display.
     *      It will enable LVGL to draw the next part of the screen to the other buffer while
     *      the data is being sent form the first buffer. It makes rendering and flushing parallel.
     *
     * 3. Double buffering
     *      Set 2 screens sized buffers and set disp_drv.full_refresh = 1.
     *      This way LVGL will always provide the whole rendered screen in `flush_cb`
     *      and you only need to change the frame buffer's address.
     */

    /* Example for 1) - Single buffer (commented out for better performance) */
    // static lv_disp_draw_buf_t draw_buf_dsc_1;
    // static lv_color_t buf_1[MY_DISP_HOR_RES * 10];                          /*A buffer for 10 rows*/
    // lv_disp_draw_buf_init(&draw_buf_dsc_1, buf_1, NULL, MY_DISP_HOR_RES * 10);   /*Initialize the display buffer*/

    /* Optimized buffer configuration for better performance */
    static lv_disp_draw_buf_t draw_buf_dsc_optimized;

    /* Use moderate buffer size and allocate in PSRAM to avoid DRAM overflow */
    #define BUFFER_LINES 60  /* 60 lines to balance performance and memory usage */

    /* Allocate buffers in PSRAM to save internal DRAM */
    lv_color_t *buf_opt_1 = heap_caps_malloc(MY_DISP_HOR_RES * BUFFER_LINES * sizeof(lv_color_t), MALLOC_CAP_SPIRAM);
    lv_color_t *buf_opt_2 = heap_caps_malloc(MY_DISP_HOR_RES * BUFFER_LINES * sizeof(lv_color_t), MALLOC_CAP_SPIRAM);

    if (buf_opt_1 && buf_opt_2) {
        lv_disp_draw_buf_init(&draw_buf_dsc_optimized, buf_opt_1, buf_opt_2, MY_DISP_HOR_RES * BUFFER_LINES);
        ESP_LOGI("LVGL_INIT", "PSRAM buffers allocated successfully: %d lines per buffer", BUFFER_LINES);
    } else {
        ESP_LOGE("LVGL_INIT", "Failed to allocate PSRAM buffers, falling back to smaller internal buffers");
        /* Fallback to smaller internal buffers */
        static lv_color_t buf_fallback_1[MY_DISP_HOR_RES * 20];
        static lv_color_t buf_fallback_2[MY_DISP_HOR_RES * 20];
        lv_disp_draw_buf_init(&draw_buf_dsc_optimized, buf_fallback_1, buf_fallback_2, MY_DISP_HOR_RES * 20);
    }

    /* Alternative: Full screen double buffering for maximum performance (uncomment if enough memory) */
    // static lv_disp_draw_buf_t draw_buf_dsc_fullscreen;
    // static lv_color_t buf_full_1[MY_DISP_HOR_RES * MY_DISP_VER_RES];     /* Full screen buffer */
    // static lv_color_t buf_full_2[MY_DISP_HOR_RES * MY_DISP_VER_RES];     /* Another full screen buffer */
    // lv_disp_draw_buf_init(&draw_buf_dsc_fullscreen, buf_full_1, buf_full_2, MY_DISP_HOR_RES * MY_DISP_VER_RES);

    /*-----------------------------------
     * Register the display in LVGL
     *----------------------------------*/

    static lv_disp_drv_t disp_drv;                         /*Descriptor of a display driver*/
    lv_disp_drv_init(&disp_drv);                    /*Basic initialization*/

    /*Set up the functions to access to your display*/

    /*Set the resolution of the display*/
    disp_drv.hor_res = MY_DISP_HOR_RES;
    disp_drv.ver_res = MY_DISP_VER_RES;

    /*Used to copy the buffer's content to the display*/
    disp_drv.flush_cb = disp_flush;

    /*Set a display buffer*/
    disp_drv.draw_buf = &draw_buf_dsc_optimized;

    /*Enable full refresh for better performance with larger buffers*/
    // disp_drv.full_refresh = 1;  /* Uncomment for full screen buffering */

    /* Fill a memory array with a color if you have GPU.
     * Note that, in lv_conf.h you can enable GPUs that has built-in support in LVGL.
     * But if you have a different GPU you can use with this callback.*/
    //disp_drv.gpu_fill_cb = gpu_fill;

    /* Optional: Add a callback to monitor the performance */
    disp_drv.monitor_cb = monitor_cb;

    /* Enable DMA-like optimizations */
    disp_drv.direct_mode = 0;  /* Use buffered mode for better performance */

    /* Enable partial refresh optimizations */
    disp_drv.full_refresh = 0;  /* Enable partial refresh for better performance */

    /* Set anti-aliasing for smoother graphics */
    disp_drv.antialiasing = 1;

    /* Enable screen transparency for better blending */
    disp_drv.screen_transp = 0;

    /*Finally register the driver*/
    lv_disp_drv_register(&disp_drv);
}

/**********************
 *   STATIC FUNCTIONS
 **********************/

/* Performance monitoring variables */
static uint32_t refresh_count = 0;
static uint32_t total_refresh_time = 0;
static uint32_t max_refresh_time = 0;
static uint32_t min_refresh_time = UINT32_MAX;

/* Performance monitoring callback */
static void monitor_cb(lv_disp_drv_t * disp_drv, uint32_t time, uint32_t px)
{
    LV_UNUSED(disp_drv);

    /* Update performance statistics */
    refresh_count++;
    total_refresh_time += time;

    if (time > max_refresh_time) {
        max_refresh_time = time;
    }

    if (time < min_refresh_time) {
        min_refresh_time = time;
    }

    /* Log performance data every 100 refreshes */
    if (refresh_count % 100 == 0) {
        uint32_t avg_time = total_refresh_time / refresh_count;
        uint32_t fps = (refresh_count * 1000) / total_refresh_time;

        ESP_LOGI("LVGL_PERF", "Performance Stats (after %u refreshes):", (unsigned int)refresh_count);
        ESP_LOGI("LVGL_PERF", "  Average refresh time: %u ms", (unsigned int)avg_time);
        ESP_LOGI("LVGL_PERF", "  Min refresh time: %u ms", (unsigned int)min_refresh_time);
        ESP_LOGI("LVGL_PERF", "  Max refresh time: %u ms", (unsigned int)max_refresh_time);
        ESP_LOGI("LVGL_PERF", "  Average FPS: %u", (unsigned int)fps);
        ESP_LOGI("LVGL_PERF", "  Last refresh: %u ms, %u pixels", (unsigned int)time, (unsigned int)px);
        ESP_LOGI("LVGL_PERF", "  Pixels per ms: %u", (unsigned int)(px / (time + 1)));
        ESP_LOGI("LVGL_PERF", "----------------------------------------");
    }
}

/*Initialize your display and the required peripherals.*/
static void disp_init(void)
{
    
    ltdc_init();
}

volatile bool disp_flush_enabled = true;

/* Enable updating the screen (the flushing process) when disp_flush() is called by LVGL
 */
void disp_enable_update(void)
{
    disp_flush_enabled = true;
}

/* Disable updating the screen (the flushing process) when disp_flush() is called by LVGL
 */
void disp_disable_update(void)
{
    disp_flush_enabled = false;
}

/* Reset performance monitoring statistics */
void disp_reset_performance_stats(void)
{
    refresh_count = 0;
    total_refresh_time = 0;
    max_refresh_time = 0;
    min_refresh_time = UINT32_MAX;
    ESP_LOGI("LVGL_PERF", "Performance statistics reset");
}

/*Flush the content of the internal buffer the specific area on the display
 *You can use DMA or any hardware acceleration to do this operation in the background but
 *'lv_disp_flush_ready()' has to be called when finished.*/
static void disp_flush(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p)
{
    if(disp_flush_enabled) {
        /*Use optimized DMA-based batch transfer for maximum performance*/

        int32_t w = area->x2 - area->x1 + 1;
        int32_t h = area->y2 - area->y1 + 1;
        int32_t total_pixels = w * h;

        /* Check if it's a single color fill (optimization for solid color areas) */
        bool is_single_color = true;
        lv_color_t first_color = color_p[0];

        /* Only check for single color if area is reasonably large to make optimization worthwhile */
        if(total_pixels > 64) {
            /* Limit check to first 64 pixels for performance */
            int check_limit = (total_pixels > 64) ? 64 : total_pixels;
            for(int i = 1; i < check_limit; i++) {
                if(color_p[i].full != first_color.full) {
                    is_single_color = false;
                    break;
                }
            }
        } else {
            is_single_color = false;
        }

        if(is_single_color && total_pixels > 64) {
            /* Use optimized color fill for large single-color areas */
            uint16_t color_565 = first_color.full;
            ltdc_color_fill(area->x1, area->y1, area->x2, area->y2, color_565);
        } else {
            /* Use DMA batch transfer for maximum performance */
            /* Convert lv_color_t array to uint16_t array for direct DMA transfer */
            uint16_t *color_data = (uint16_t *)color_p;

            /* Use the new DMA-optimized batch transfer function */
            ltdc_draw_bitmap_dma(area->x1, area->y1, area->x2, area->y2, color_data);
        }
    }

    /*IMPORTANT!!!
     *Inform the graphics library that you are ready with the flushing*/
    lv_disp_flush_ready(disp_drv);
}

/*OPTIONAL: GPU INTERFACE*/

/*If your MCU has hardware accelerator (GPU) then you can use it to fill a memory with a color*/
//static void gpu_fill(lv_disp_drv_t * disp_drv, lv_color_t * dest_buf, lv_coord_t dest_width,
//                    const lv_area_t * fill_area, lv_color_t color)
//{
//    /*It's an example code which should be done by your GPU*/
//    int32_t x, y;
//    dest_buf += dest_width * fill_area->y1; /*Go to the first line*/
//
//    for(y = fill_area->y1; y <= fill_area->y2; y++) {
//        for(x = fill_area->x1; x <= fill_area->x2; x++) {
//            dest_buf[x] = color;
//        }
//        dest_buf+=dest_width;    /*Go to the next line*/
//    }
//}


#else /*Enable this file at the top*/

/*This dummy typedef exists purely to silence -Wpedantic.*/
typedef int keep_pedantic_happy;
#endif
